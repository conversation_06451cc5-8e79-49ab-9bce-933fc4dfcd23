package com.tqhit.battery.one.activity.onboarding

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.databinding.ActivityLanguageSelectionBinding
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiState
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Language Selection Activity for the onboarding flow.
 * 
 * Features:
 * - Full-screen language selection interface adapted from fragment layout
 * - MVI pattern state management with proper ViewBinding
 * - Language selection logic reused from SelectLanguageDialog
 * - Direct navigation to StartingActivity after language selection
 * - Material 3 design consistency with onboarding flow
 * - Proper activity lifecycle management and state persistence
 * 
 * Navigation Flow: SplashActivity → LanguageSelectionActivity → StartingActivity → MainActivity
 */
@AndroidEntryPoint
class LanguageSelectionActivity : AdLibBaseActivity<ActivityLanguageSelectionBinding>() {

    companion object {
        private const val TAG = "LanguageSelectionActivity"
    }

    // ViewBinding implementation following established pattern
    override val binding by lazy {
        Log.d(TAG, "BINDING_INIT: Initializing ActivityLanguageSelectionBinding")
        try {
            ActivityLanguageSelectionBinding.inflate(layoutInflater)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_INIT: Critical error initializing ViewBinding", e)
            throw e
        }
    }

    // ViewModels
    private val appViewModel: AppViewModel by viewModels()
    private val languageSelectionViewModel: LanguageSelectionViewModel by viewModels()

    // Native Ad Manager
    @Inject lateinit var applovinNativeAdManager: ApplovinNativeAdManager
    private var nativeAdView: MaxNativeAdView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: LanguageSelectionActivity.onCreate() started at $startTime")

        super.onCreate(savedInstanceState)

        Log.d(TAG, "STARTUP_TIMING: LanguageSelectionActivity.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity data setup completed")
    }

    override fun setupUI() {
        super.setupUI()

        val setupStartTime = System.currentTimeMillis()
        setupLanguageButtons()
        updateSelectedLanguageUI()
        setupNativeAd()
        Log.d(TAG, "STARTUP_TIMING: UI setup took ${System.currentTimeMillis() - setupStartTime}ms")

        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity UI setup completed")
    }

    override fun setupListener() {
        super.setupListener()

        // Setup Next button listener
        setupNextButtonListener()

        // Observe ViewModel state
        observeLanguageSelectionState()

        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity listeners setup completed")
    }

    /**
     * Sets up click listeners for all language option buttons.
     * Reuses the same language codes and logic from SelectLanguageDialog.
     */
    private fun setupLanguageButtons() {
        safeBindingAccess("setupLanguageButtons") { binding ->
            // Setup click listeners for each language option
            binding.de.setOnClickListener { selectLanguage("de") } // German
            binding.nl.setOnClickListener { selectLanguage("nl") } // Dutch
            binding.en.setOnClickListener { selectLanguage("en") } // English
            binding.es.setOnClickListener { selectLanguage("es") } // Spanish
            binding.fr.setOnClickListener { selectLanguage("fr") } // French
            binding.it.setOnClickListener { selectLanguage("it") } // Italian
            binding.hu.setOnClickListener { selectLanguage("hu") } // Hungarian
            binding.pl.setOnClickListener { selectLanguage("pl") } // Polish
            binding.pt.setOnClickListener { selectLanguage("pt") } // Portuguese
            binding.ro.setOnClickListener { selectLanguage("ro") } // Romanian
            binding.tr.setOnClickListener { selectLanguage("tr") } // Turkish
            binding.ru.setOnClickListener { selectLanguage("ru") } // Russian
            binding.ua.setOnClickListener { selectLanguage("uk") } // Ukrainian
            binding.ar.setOnClickListener { selectLanguage("ar") } // Arabic
            binding.zh.setOnClickListener { selectLanguage("zh") } // Chinese
            
            Log.d(TAG, "LANGUAGE_SELECTION: Language button listeners setup completed")
        }
    }

    /**
     * Updates the UI to show the currently selected language.
     * Applies the same visual selection logic as SelectLanguageDialog.
     */
    private fun updateSelectedLanguageUI() {
        safeBindingAccess("updateSelectedLanguageUI") { binding ->
            val currentLanguage = appViewModel.getLanguage()
            Log.d(TAG, "LANGUAGE_SELECTION: Updating UI for current language: $currentLanguage")

            // Set backgrounds and selection state based on current language
            binding.de.apply {
                isSelected = currentLanguage == "de"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_up else R.drawable.grey_block_line_up)
            }

            binding.nl.apply {
                isSelected = currentLanguage == "nl"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.en.apply {
                isSelected = currentLanguage == "en"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.es.apply {
                isSelected = currentLanguage == "es"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.fr.apply {
                isSelected = currentLanguage == "fr"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.it.apply {
                isSelected = currentLanguage == "it"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.hu.apply {
                isSelected = currentLanguage == "hu"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.pl.apply {
                isSelected = currentLanguage == "pl"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.pt.apply {
                isSelected = currentLanguage == "pt"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ro.apply {
                isSelected = currentLanguage == "ro"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.tr.apply {
                isSelected = currentLanguage == "tr"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ru.apply {
                isSelected = currentLanguage == "ru"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ua.apply {
                isSelected = currentLanguage == "uk"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ar.apply {
                isSelected = currentLanguage == "ar"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.zh.apply {
                isSelected = currentLanguage == "zh"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_down else R.drawable.grey_block_line_down)
            }
        }
    }

    /**
     * Handles language selection and triggers navigation to StartingActivity.
     */
    private fun selectLanguage(languageCode: String) {
        Log.d(TAG, "LANGUAGE_SELECTION: User selected language: $languageCode")
        
        // Trigger language selection through ViewModel
        languageSelectionViewModel.selectLanguage(languageCode)
    }

    /**
     * Observes language selection state and handles navigation.
     */
    private fun observeLanguageSelectionState() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                languageSelectionViewModel.uiState.collect { state ->
                    when (state) {
                        is LanguageSelectionUiState.Idle -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Idle")
                            hideNextButton()
                        }
                        is LanguageSelectionUiState.LanguageSelected -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Language selected: ${state.languageCode}")
                            handleLanguageSelected(state.languageCode)
                            showNextButton()
                        }
                        is LanguageSelectionUiState.LanguageConfirmed -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Language confirmed: ${state.languageCode}")
                            handleLanguageConfirmed(state.languageCode)
                        }
                        is LanguageSelectionUiState.NavigatingToOnboarding -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Navigating to onboarding")
                            navigateToStartingActivity()
                        }
                    }
                }
            }
        }
    }

    /**
     * Handles the language selection (first step).
     * Updates UI selection but doesn't navigate yet.
     */
    private fun handleLanguageSelected(languageCode: String) {
        // Update UI to reflect selection
        updateSelectedLanguageUI()

        Log.d(TAG, "LANGUAGE_SELECTION: Language $languageCode selected - Next button shown")
    }

    /**
     * Handles the language confirmation (second step).
     * Saves language and triggers navigation.
     */
    private fun handleLanguageConfirmed(languageCode: String) {
        // Save language using AppViewModel
        appViewModel.setLanguage(languageCode)

        // Apply the new language
        appViewModel.setLocale(this, languageCode)

        // Trigger navigation after a short delay for visual feedback
        languageSelectionViewModel.proceedToOnboarding()

        Log.d(TAG, "LANGUAGE_SELECTION: Language $languageCode confirmed and applied")
    }

    /**
     * Navigates directly to StartingActivity for the original onboarding slides.
     */
    private fun navigateToStartingActivity() {
        try {
            val intent = Intent(this, StartingActivity::class.java)
            startActivity(intent)
            finish()
            
            Log.d(TAG, "LANGUAGE_SELECTION: Successfully navigated to StartingActivity")
        } catch (e: Exception) {
            Log.e(TAG, "LANGUAGE_SELECTION: Error navigating to StartingActivity", e)
        }
    }

    /**
     * Shows the Next button after language selection.
     */
    private fun showNextButton() {
        safeBindingAccess("showNextButton") { binding ->
            binding.nextButton.visibility = View.VISIBLE
            Log.d(TAG, "LANGUAGE_SELECTION: Next button shown")
        }
    }

    /**
     * Hides the Next button.
     */
    private fun hideNextButton() {
        safeBindingAccess("hideNextButton") { binding ->
            binding.nextButton.visibility = View.GONE
            Log.d(TAG, "LANGUAGE_SELECTION: Next button hidden")
        }
    }

    /**
     * Sets up the Next button click listener.
     */
    private fun setupNextButtonListener() {
        safeBindingAccess("setupNextButtonListener") { binding ->
            binding.nextButton.setOnClickListener {
                Log.d(TAG, "LANGUAGE_SELECTION: Next button clicked")
                languageSelectionViewModel.confirmLanguageSelection()
            }
            Log.d(TAG, "LANGUAGE_SELECTION: Next button listener setup completed")
        }
    }

    /**
     * Sets up native ad following StartingActivity pattern.
     */
    private fun setupNativeAd() {
        safeBindingAccess("setupNativeAd") { binding ->
            val container = binding.nativeAd
            val nativeAdView = createNativeAdView()

            applovinNativeAdManager.loadNativeAd(
                nativeAdView = nativeAdView,
                onAdLoaded = {
                    container.removeAllViews()
                    container.hideShimmer()
                    container.addView(it)
                    Log.d(TAG, "NATIVE_AD: Native ad loaded successfully")
                },
                onAdLoadFailed = { errorMsg ->
                    Log.e(TAG, "NATIVE_AD: Failed to load native ad: $errorMsg")
                }
            )
        }
    }

    /**
     * Creates native ad view following StartingActivity pattern.
     */
    private fun createNativeAdView(): MaxNativeAdView {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view)
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, this)
    }

    /**
     * Safe binding access method with error handling.
     * Follows the established pattern from other activities.
     */
    private fun safeBindingAccess(operation: String, action: (ActivityLanguageSelectionBinding) -> Unit) {
        try {
            action(binding)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_ACCESS: Error in $operation", e)
        }
    }

    override fun onDestroy() {
        applovinNativeAdManager.destroy()
        super.onDestroy()
        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity destroyed")
    }
}
